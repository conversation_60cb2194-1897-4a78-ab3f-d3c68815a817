2025-10-09 10:18:03,238 ERROR scheduler Exception in Enqueue Events for Site local.westside
Traceback (most recent call last):
  File "/home/<USER>/westside-bench/apps/frappe/frappe/utils/scheduler.py", line 99, in enqueue_events_for_site
    if is_scheduler_inactive():
       ^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/westside-bench/apps/frappe/frappe/utils/scheduler.py", line 143, in is_scheduler_inactive
    if is_scheduler_disabled(verbose=verbose):
       ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/westside-bench/apps/frappe/frappe/utils/scheduler.py", line 156, in is_scheduler_disabled
    frappe.db.get_single_value("System Settings", "enable_scheduler")
  File "/home/<USER>/westside-bench/apps/frappe/frappe/database/database.py", line 837, in get_single_value
    ).run()
      ^^^^^
  File "/home/<USER>/westside-bench/apps/frappe/frappe/query_builder/utils.py", line 87, in execute_query
    result = frappe.db.sql(query, params, *args, **kwargs)  # nosemgrep
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/westside-bench/apps/frappe/frappe/database/database.py", line 207, in sql
    self.connect()
  File "/home/<USER>/westside-bench/apps/frappe/frappe/database/database.py", line 112, in connect
    self._conn: "MariadbConnection" | "PostgresConnection" = self.get_connection()
                                                             ^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/westside-bench/apps/frappe/frappe/database/mariadb/database.py", line 107, in get_connection
    conn = self._get_connection()
           ^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/westside-bench/apps/frappe/frappe/database/mariadb/database.py", line 113, in _get_connection
    return self.create_connection()
           ^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/westside-bench/apps/frappe/frappe/database/mariadb/database.py", line 116, in create_connection
    return pymysql.connect(**self.get_connection_settings())
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/westside-bench/env/lib/python3.12/site-packages/pymysql/connections.py", line 361, in __init__
    self.connect()
  File "/home/<USER>/westside-bench/env/lib/python3.12/site-packages/pymysql/connections.py", line 669, in connect
    self._request_authentication()
  File "/home/<USER>/westside-bench/env/lib/python3.12/site-packages/pymysql/connections.py", line 957, in _request_authentication
    auth_packet = self._read_packet()
                  ^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/westside-bench/env/lib/python3.12/site-packages/pymysql/connections.py", line 775, in _read_packet
    packet.raise_for_error()
  File "/home/<USER>/westside-bench/env/lib/python3.12/site-packages/pymysql/protocol.py", line 219, in raise_for_error
    err.raise_mysql_exception(self._data)
  File "/home/<USER>/westside-bench/env/lib/python3.12/site-packages/pymysql/err.py", line 150, in raise_mysql_exception
    raise errorclass(errno, errval)
pymysql.err.OperationalError: (1045, "Access denied for user '_07fdbf6ba2f0be13'@'localhost' (using password: YES)")
2025-10-09 10:19:03,251 ERROR scheduler Exception in Enqueue Events for Site local.westside
Traceback (most recent call last):
  File "/home/<USER>/westside-bench/apps/frappe/frappe/utils/scheduler.py", line 99, in enqueue_events_for_site
    if is_scheduler_inactive():
       ^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/westside-bench/apps/frappe/frappe/utils/scheduler.py", line 143, in is_scheduler_inactive
    if is_scheduler_disabled(verbose=verbose):
       ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/westside-bench/apps/frappe/frappe/utils/scheduler.py", line 156, in is_scheduler_disabled
    frappe.db.get_single_value("System Settings", "enable_scheduler")
  File "/home/<USER>/westside-bench/apps/frappe/frappe/database/database.py", line 837, in get_single_value
    ).run()
      ^^^^^
  File "/home/<USER>/westside-bench/apps/frappe/frappe/query_builder/utils.py", line 87, in execute_query
    result = frappe.db.sql(query, params, *args, **kwargs)  # nosemgrep
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/westside-bench/apps/frappe/frappe/database/database.py", line 207, in sql
    self.connect()
  File "/home/<USER>/westside-bench/apps/frappe/frappe/database/database.py", line 112, in connect
    self._conn: "MariadbConnection" | "PostgresConnection" = self.get_connection()
                                                             ^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/westside-bench/apps/frappe/frappe/database/mariadb/database.py", line 107, in get_connection
    conn = self._get_connection()
           ^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/westside-bench/apps/frappe/frappe/database/mariadb/database.py", line 113, in _get_connection
    return self.create_connection()
           ^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/westside-bench/apps/frappe/frappe/database/mariadb/database.py", line 116, in create_connection
    return pymysql.connect(**self.get_connection_settings())
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/westside-bench/env/lib/python3.12/site-packages/pymysql/connections.py", line 361, in __init__
    self.connect()
  File "/home/<USER>/westside-bench/env/lib/python3.12/site-packages/pymysql/connections.py", line 669, in connect
    self._request_authentication()
  File "/home/<USER>/westside-bench/env/lib/python3.12/site-packages/pymysql/connections.py", line 957, in _request_authentication
    auth_packet = self._read_packet()
                  ^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/westside-bench/env/lib/python3.12/site-packages/pymysql/connections.py", line 775, in _read_packet
    packet.raise_for_error()
  File "/home/<USER>/westside-bench/env/lib/python3.12/site-packages/pymysql/protocol.py", line 219, in raise_for_error
    err.raise_mysql_exception(self._data)
  File "/home/<USER>/westside-bench/env/lib/python3.12/site-packages/pymysql/err.py", line 150, in raise_mysql_exception
    raise errorclass(errno, errval)
pymysql.err.OperationalError: (1045, "Access denied for user '_07fdbf6ba2f0be13'@'localhost' (using password: YES)")
2025-10-09 10:20:03,323 ERROR scheduler Exception in Enqueue Events for Site local.westside
Traceback (most recent call last):
  File "/home/<USER>/westside-bench/apps/frappe/frappe/utils/scheduler.py", line 99, in enqueue_events_for_site
    if is_scheduler_inactive():
       ^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/westside-bench/apps/frappe/frappe/utils/scheduler.py", line 143, in is_scheduler_inactive
    if is_scheduler_disabled(verbose=verbose):
       ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/westside-bench/apps/frappe/frappe/utils/scheduler.py", line 156, in is_scheduler_disabled
    frappe.db.get_single_value("System Settings", "enable_scheduler")
  File "/home/<USER>/westside-bench/apps/frappe/frappe/database/database.py", line 837, in get_single_value
    ).run()
      ^^^^^
  File "/home/<USER>/westside-bench/apps/frappe/frappe/query_builder/utils.py", line 87, in execute_query
    result = frappe.db.sql(query, params, *args, **kwargs)  # nosemgrep
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/westside-bench/apps/frappe/frappe/database/database.py", line 207, in sql
    self.connect()
  File "/home/<USER>/westside-bench/apps/frappe/frappe/database/database.py", line 112, in connect
    self._conn: "MariadbConnection" | "PostgresConnection" = self.get_connection()
                                                             ^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/westside-bench/apps/frappe/frappe/database/mariadb/database.py", line 107, in get_connection
    conn = self._get_connection()
           ^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/westside-bench/apps/frappe/frappe/database/mariadb/database.py", line 113, in _get_connection
    return self.create_connection()
           ^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/westside-bench/apps/frappe/frappe/database/mariadb/database.py", line 116, in create_connection
    return pymysql.connect(**self.get_connection_settings())
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/westside-bench/env/lib/python3.12/site-packages/pymysql/connections.py", line 361, in __init__
    self.connect()
  File "/home/<USER>/westside-bench/env/lib/python3.12/site-packages/pymysql/connections.py", line 669, in connect
    self._request_authentication()
  File "/home/<USER>/westside-bench/env/lib/python3.12/site-packages/pymysql/connections.py", line 957, in _request_authentication
    auth_packet = self._read_packet()
                  ^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/westside-bench/env/lib/python3.12/site-packages/pymysql/connections.py", line 775, in _read_packet
    packet.raise_for_error()
  File "/home/<USER>/westside-bench/env/lib/python3.12/site-packages/pymysql/protocol.py", line 219, in raise_for_error
    err.raise_mysql_exception(self._data)
  File "/home/<USER>/westside-bench/env/lib/python3.12/site-packages/pymysql/err.py", line 150, in raise_mysql_exception
    raise errorclass(errno, errval)
pymysql.err.OperationalError: (1045, "Access denied for user '_07fdbf6ba2f0be13'@'localhost' (using password: YES)")
2025-10-09 10:21:03,414 ERROR scheduler Exception in Enqueue Events for Site local.westside
Traceback (most recent call last):
  File "/home/<USER>/westside-bench/apps/frappe/frappe/utils/scheduler.py", line 99, in enqueue_events_for_site
    if is_scheduler_inactive():
       ^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/westside-bench/apps/frappe/frappe/utils/scheduler.py", line 143, in is_scheduler_inactive
    if is_scheduler_disabled(verbose=verbose):
       ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/westside-bench/apps/frappe/frappe/utils/scheduler.py", line 156, in is_scheduler_disabled
    frappe.db.get_single_value("System Settings", "enable_scheduler")
  File "/home/<USER>/westside-bench/apps/frappe/frappe/database/database.py", line 837, in get_single_value
    ).run()
      ^^^^^
  File "/home/<USER>/westside-bench/apps/frappe/frappe/query_builder/utils.py", line 87, in execute_query
    result = frappe.db.sql(query, params, *args, **kwargs)  # nosemgrep
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/westside-bench/apps/frappe/frappe/database/database.py", line 207, in sql
    self.connect()
  File "/home/<USER>/westside-bench/apps/frappe/frappe/database/database.py", line 112, in connect
    self._conn: "MariadbConnection" | "PostgresConnection" = self.get_connection()
                                                             ^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/westside-bench/apps/frappe/frappe/database/mariadb/database.py", line 107, in get_connection
    conn = self._get_connection()
           ^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/westside-bench/apps/frappe/frappe/database/mariadb/database.py", line 113, in _get_connection
    return self.create_connection()
           ^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/westside-bench/apps/frappe/frappe/database/mariadb/database.py", line 116, in create_connection
    return pymysql.connect(**self.get_connection_settings())
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/westside-bench/env/lib/python3.12/site-packages/pymysql/connections.py", line 361, in __init__
    self.connect()
  File "/home/<USER>/westside-bench/env/lib/python3.12/site-packages/pymysql/connections.py", line 669, in connect
    self._request_authentication()
  File "/home/<USER>/westside-bench/env/lib/python3.12/site-packages/pymysql/connections.py", line 957, in _request_authentication
    auth_packet = self._read_packet()
                  ^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/westside-bench/env/lib/python3.12/site-packages/pymysql/connections.py", line 775, in _read_packet
    packet.raise_for_error()
  File "/home/<USER>/westside-bench/env/lib/python3.12/site-packages/pymysql/protocol.py", line 219, in raise_for_error
    err.raise_mysql_exception(self._data)
  File "/home/<USER>/westside-bench/env/lib/python3.12/site-packages/pymysql/err.py", line 150, in raise_mysql_exception
    raise errorclass(errno, errval)
pymysql.err.OperationalError: (1045, "Access denied for user '_07fdbf6ba2f0be13'@'localhost' (using password: YES)")
2025-10-09 10:22:03,569 ERROR scheduler Exception in Enqueue Events for Site local.westside
Traceback (most recent call last):
  File "/home/<USER>/westside-bench/apps/frappe/frappe/utils/scheduler.py", line 99, in enqueue_events_for_site
    if is_scheduler_inactive():
       ^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/westside-bench/apps/frappe/frappe/utils/scheduler.py", line 143, in is_scheduler_inactive
    if is_scheduler_disabled(verbose=verbose):
       ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/westside-bench/apps/frappe/frappe/utils/scheduler.py", line 156, in is_scheduler_disabled
    frappe.db.get_single_value("System Settings", "enable_scheduler")
  File "/home/<USER>/westside-bench/apps/frappe/frappe/database/database.py", line 837, in get_single_value
    ).run()
      ^^^^^
  File "/home/<USER>/westside-bench/apps/frappe/frappe/query_builder/utils.py", line 87, in execute_query
    result = frappe.db.sql(query, params, *args, **kwargs)  # nosemgrep
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/westside-bench/apps/frappe/frappe/database/database.py", line 207, in sql
    self.connect()
  File "/home/<USER>/westside-bench/apps/frappe/frappe/database/database.py", line 112, in connect
    self._conn: "MariadbConnection" | "PostgresConnection" = self.get_connection()
                                                             ^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/westside-bench/apps/frappe/frappe/database/mariadb/database.py", line 107, in get_connection
    conn = self._get_connection()
           ^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/westside-bench/apps/frappe/frappe/database/mariadb/database.py", line 113, in _get_connection
    return self.create_connection()
           ^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/westside-bench/apps/frappe/frappe/database/mariadb/database.py", line 116, in create_connection
    return pymysql.connect(**self.get_connection_settings())
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/westside-bench/env/lib/python3.12/site-packages/pymysql/connections.py", line 361, in __init__
    self.connect()
  File "/home/<USER>/westside-bench/env/lib/python3.12/site-packages/pymysql/connections.py", line 669, in connect
    self._request_authentication()
  File "/home/<USER>/westside-bench/env/lib/python3.12/site-packages/pymysql/connections.py", line 957, in _request_authentication
    auth_packet = self._read_packet()
                  ^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/westside-bench/env/lib/python3.12/site-packages/pymysql/connections.py", line 775, in _read_packet
    packet.raise_for_error()
  File "/home/<USER>/westside-bench/env/lib/python3.12/site-packages/pymysql/protocol.py", line 219, in raise_for_error
    err.raise_mysql_exception(self._data)
  File "/home/<USER>/westside-bench/env/lib/python3.12/site-packages/pymysql/err.py", line 150, in raise_mysql_exception
    raise errorclass(errno, errval)
pymysql.err.OperationalError: (1045, "Access denied for user '_07fdbf6ba2f0be13'@'localhost' (using password: YES)")
2025-10-09 10:23:03,576 ERROR scheduler Exception in Enqueue Events for Site local.westside
Traceback (most recent call last):
  File "/home/<USER>/westside-bench/apps/frappe/frappe/utils/scheduler.py", line 99, in enqueue_events_for_site
    if is_scheduler_inactive():
       ^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/westside-bench/apps/frappe/frappe/utils/scheduler.py", line 143, in is_scheduler_inactive
    if is_scheduler_disabled(verbose=verbose):
       ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/westside-bench/apps/frappe/frappe/utils/scheduler.py", line 156, in is_scheduler_disabled
    frappe.db.get_single_value("System Settings", "enable_scheduler")
  File "/home/<USER>/westside-bench/apps/frappe/frappe/database/database.py", line 837, in get_single_value
    ).run()
      ^^^^^
  File "/home/<USER>/westside-bench/apps/frappe/frappe/query_builder/utils.py", line 87, in execute_query
    result = frappe.db.sql(query, params, *args, **kwargs)  # nosemgrep
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/westside-bench/apps/frappe/frappe/database/database.py", line 207, in sql
    self.connect()
  File "/home/<USER>/westside-bench/apps/frappe/frappe/database/database.py", line 112, in connect
    self._conn: "MariadbConnection" | "PostgresConnection" = self.get_connection()
                                                             ^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/westside-bench/apps/frappe/frappe/database/mariadb/database.py", line 107, in get_connection
    conn = self._get_connection()
           ^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/westside-bench/apps/frappe/frappe/database/mariadb/database.py", line 113, in _get_connection
    return self.create_connection()
           ^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/westside-bench/apps/frappe/frappe/database/mariadb/database.py", line 116, in create_connection
    return pymysql.connect(**self.get_connection_settings())
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/westside-bench/env/lib/python3.12/site-packages/pymysql/connections.py", line 361, in __init__
    self.connect()
  File "/home/<USER>/westside-bench/env/lib/python3.12/site-packages/pymysql/connections.py", line 669, in connect
    self._request_authentication()
  File "/home/<USER>/westside-bench/env/lib/python3.12/site-packages/pymysql/connections.py", line 957, in _request_authentication
    auth_packet = self._read_packet()
                  ^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/westside-bench/env/lib/python3.12/site-packages/pymysql/connections.py", line 775, in _read_packet
    packet.raise_for_error()
  File "/home/<USER>/westside-bench/env/lib/python3.12/site-packages/pymysql/protocol.py", line 219, in raise_for_error
    err.raise_mysql_exception(self._data)
  File "/home/<USER>/westside-bench/env/lib/python3.12/site-packages/pymysql/err.py", line 150, in raise_mysql_exception
    raise errorclass(errno, errval)
pymysql.err.OperationalError: (1045, "Access denied for user '_07fdbf6ba2f0be13'@'localhost' (using password: YES)")
2025-10-09 10:24:03,656 ERROR scheduler Exception in Enqueue Events for Site local.westside
Traceback (most recent call last):
  File "/home/<USER>/westside-bench/apps/frappe/frappe/utils/scheduler.py", line 99, in enqueue_events_for_site
    if is_scheduler_inactive():
       ^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/westside-bench/apps/frappe/frappe/utils/scheduler.py", line 143, in is_scheduler_inactive
    if is_scheduler_disabled(verbose=verbose):
       ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/westside-bench/apps/frappe/frappe/utils/scheduler.py", line 156, in is_scheduler_disabled
    frappe.db.get_single_value("System Settings", "enable_scheduler")
  File "/home/<USER>/westside-bench/apps/frappe/frappe/database/database.py", line 837, in get_single_value
    ).run()
      ^^^^^
  File "/home/<USER>/westside-bench/apps/frappe/frappe/query_builder/utils.py", line 87, in execute_query
    result = frappe.db.sql(query, params, *args, **kwargs)  # nosemgrep
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/westside-bench/apps/frappe/frappe/database/database.py", line 207, in sql
    self.connect()
  File "/home/<USER>/westside-bench/apps/frappe/frappe/database/database.py", line 112, in connect
    self._conn: "MariadbConnection" | "PostgresConnection" = self.get_connection()
                                                             ^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/westside-bench/apps/frappe/frappe/database/mariadb/database.py", line 107, in get_connection
    conn = self._get_connection()
           ^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/westside-bench/apps/frappe/frappe/database/mariadb/database.py", line 113, in _get_connection
    return self.create_connection()
           ^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/westside-bench/apps/frappe/frappe/database/mariadb/database.py", line 116, in create_connection
    return pymysql.connect(**self.get_connection_settings())
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/westside-bench/env/lib/python3.12/site-packages/pymysql/connections.py", line 361, in __init__
    self.connect()
  File "/home/<USER>/westside-bench/env/lib/python3.12/site-packages/pymysql/connections.py", line 669, in connect
    self._request_authentication()
  File "/home/<USER>/westside-bench/env/lib/python3.12/site-packages/pymysql/connections.py", line 957, in _request_authentication
    auth_packet = self._read_packet()
                  ^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/westside-bench/env/lib/python3.12/site-packages/pymysql/connections.py", line 775, in _read_packet
    packet.raise_for_error()
  File "/home/<USER>/westside-bench/env/lib/python3.12/site-packages/pymysql/protocol.py", line 219, in raise_for_error
    err.raise_mysql_exception(self._data)
  File "/home/<USER>/westside-bench/env/lib/python3.12/site-packages/pymysql/err.py", line 150, in raise_mysql_exception
    raise errorclass(errno, errval)
pymysql.err.OperationalError: (1045, "Access denied for user '_07fdbf6ba2f0be13'@'localhost' (using password: YES)")
2025-10-09 10:25:03,812 ERROR scheduler Exception in Enqueue Events for Site local.westside
Traceback (most recent call last):
  File "/home/<USER>/westside-bench/apps/frappe/frappe/utils/scheduler.py", line 99, in enqueue_events_for_site
    if is_scheduler_inactive():
       ^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/westside-bench/apps/frappe/frappe/utils/scheduler.py", line 143, in is_scheduler_inactive
    if is_scheduler_disabled(verbose=verbose):
       ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/westside-bench/apps/frappe/frappe/utils/scheduler.py", line 156, in is_scheduler_disabled
    frappe.db.get_single_value("System Settings", "enable_scheduler")
  File "/home/<USER>/westside-bench/apps/frappe/frappe/database/database.py", line 837, in get_single_value
    ).run()
      ^^^^^
  File "/home/<USER>/westside-bench/apps/frappe/frappe/query_builder/utils.py", line 87, in execute_query
    result = frappe.db.sql(query, params, *args, **kwargs)  # nosemgrep
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/westside-bench/apps/frappe/frappe/database/database.py", line 207, in sql
    self.connect()
  File "/home/<USER>/westside-bench/apps/frappe/frappe/database/database.py", line 112, in connect
    self._conn: "MariadbConnection" | "PostgresConnection" = self.get_connection()
                                                             ^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/westside-bench/apps/frappe/frappe/database/mariadb/database.py", line 107, in get_connection
    conn = self._get_connection()
           ^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/westside-bench/apps/frappe/frappe/database/mariadb/database.py", line 113, in _get_connection
    return self.create_connection()
           ^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/westside-bench/apps/frappe/frappe/database/mariadb/database.py", line 116, in create_connection
    return pymysql.connect(**self.get_connection_settings())
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/westside-bench/env/lib/python3.12/site-packages/pymysql/connections.py", line 361, in __init__
    self.connect()
  File "/home/<USER>/westside-bench/env/lib/python3.12/site-packages/pymysql/connections.py", line 669, in connect
    self._request_authentication()
  File "/home/<USER>/westside-bench/env/lib/python3.12/site-packages/pymysql/connections.py", line 957, in _request_authentication
    auth_packet = self._read_packet()
                  ^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/westside-bench/env/lib/python3.12/site-packages/pymysql/connections.py", line 775, in _read_packet
    packet.raise_for_error()
  File "/home/<USER>/westside-bench/env/lib/python3.12/site-packages/pymysql/protocol.py", line 219, in raise_for_error
    err.raise_mysql_exception(self._data)
  File "/home/<USER>/westside-bench/env/lib/python3.12/site-packages/pymysql/err.py", line 150, in raise_mysql_exception
    raise errorclass(errno, errval)
pymysql.err.OperationalError: (1045, "Access denied for user '_07fdbf6ba2f0be13'@'localhost' (using password: YES)")
2025-10-09 10:26:03,876 ERROR scheduler Exception in Enqueue Events for Site local.westside
Traceback (most recent call last):
  File "/home/<USER>/westside-bench/apps/frappe/frappe/utils/scheduler.py", line 99, in enqueue_events_for_site
    if is_scheduler_inactive():
       ^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/westside-bench/apps/frappe/frappe/utils/scheduler.py", line 143, in is_scheduler_inactive
    if is_scheduler_disabled(verbose=verbose):
       ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/westside-bench/apps/frappe/frappe/utils/scheduler.py", line 156, in is_scheduler_disabled
    frappe.db.get_single_value("System Settings", "enable_scheduler")
  File "/home/<USER>/westside-bench/apps/frappe/frappe/database/database.py", line 837, in get_single_value
    ).run()
      ^^^^^
  File "/home/<USER>/westside-bench/apps/frappe/frappe/query_builder/utils.py", line 87, in execute_query
    result = frappe.db.sql(query, params, *args, **kwargs)  # nosemgrep
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/westside-bench/apps/frappe/frappe/database/database.py", line 207, in sql
    self.connect()
  File "/home/<USER>/westside-bench/apps/frappe/frappe/database/database.py", line 112, in connect
    self._conn: "MariadbConnection" | "PostgresConnection" = self.get_connection()
                                                             ^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/westside-bench/apps/frappe/frappe/database/mariadb/database.py", line 107, in get_connection
    conn = self._get_connection()
           ^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/westside-bench/apps/frappe/frappe/database/mariadb/database.py", line 113, in _get_connection
    return self.create_connection()
           ^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/westside-bench/apps/frappe/frappe/database/mariadb/database.py", line 116, in create_connection
    return pymysql.connect(**self.get_connection_settings())
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/westside-bench/env/lib/python3.12/site-packages/pymysql/connections.py", line 361, in __init__
    self.connect()
  File "/home/<USER>/westside-bench/env/lib/python3.12/site-packages/pymysql/connections.py", line 669, in connect
    self._request_authentication()
  File "/home/<USER>/westside-bench/env/lib/python3.12/site-packages/pymysql/connections.py", line 957, in _request_authentication
    auth_packet = self._read_packet()
                  ^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/westside-bench/env/lib/python3.12/site-packages/pymysql/connections.py", line 775, in _read_packet
    packet.raise_for_error()
  File "/home/<USER>/westside-bench/env/lib/python3.12/site-packages/pymysql/protocol.py", line 219, in raise_for_error
    err.raise_mysql_exception(self._data)
  File "/home/<USER>/westside-bench/env/lib/python3.12/site-packages/pymysql/err.py", line 150, in raise_mysql_exception
    raise errorclass(errno, errval)
pymysql.err.OperationalError: (1045, "Access denied for user '_07fdbf6ba2f0be13'@'localhost' (using password: YES)")
2025-10-09 10:27:03,882 ERROR scheduler Exception in Enqueue Events for Site local.westside
Traceback (most recent call last):
  File "/home/<USER>/westside-bench/apps/frappe/frappe/utils/scheduler.py", line 99, in enqueue_events_for_site
    if is_scheduler_inactive():
       ^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/westside-bench/apps/frappe/frappe/utils/scheduler.py", line 143, in is_scheduler_inactive
    if is_scheduler_disabled(verbose=verbose):
       ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/westside-bench/apps/frappe/frappe/utils/scheduler.py", line 156, in is_scheduler_disabled
    frappe.db.get_single_value("System Settings", "enable_scheduler")
  File "/home/<USER>/westside-bench/apps/frappe/frappe/database/database.py", line 837, in get_single_value
    ).run()
      ^^^^^
  File "/home/<USER>/westside-bench/apps/frappe/frappe/query_builder/utils.py", line 87, in execute_query
    result = frappe.db.sql(query, params, *args, **kwargs)  # nosemgrep
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/westside-bench/apps/frappe/frappe/database/database.py", line 207, in sql
    self.connect()
  File "/home/<USER>/westside-bench/apps/frappe/frappe/database/database.py", line 112, in connect
    self._conn: "MariadbConnection" | "PostgresConnection" = self.get_connection()
                                                             ^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/westside-bench/apps/frappe/frappe/database/mariadb/database.py", line 107, in get_connection
    conn = self._get_connection()
           ^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/westside-bench/apps/frappe/frappe/database/mariadb/database.py", line 113, in _get_connection
    return self.create_connection()
           ^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/westside-bench/apps/frappe/frappe/database/mariadb/database.py", line 116, in create_connection
    return pymysql.connect(**self.get_connection_settings())
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/westside-bench/env/lib/python3.12/site-packages/pymysql/connections.py", line 361, in __init__
    self.connect()
  File "/home/<USER>/westside-bench/env/lib/python3.12/site-packages/pymysql/connections.py", line 669, in connect
    self._request_authentication()
  File "/home/<USER>/westside-bench/env/lib/python3.12/site-packages/pymysql/connections.py", line 957, in _request_authentication
    auth_packet = self._read_packet()
                  ^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/westside-bench/env/lib/python3.12/site-packages/pymysql/connections.py", line 775, in _read_packet
    packet.raise_for_error()
  File "/home/<USER>/westside-bench/env/lib/python3.12/site-packages/pymysql/protocol.py", line 219, in raise_for_error
    err.raise_mysql_exception(self._data)
  File "/home/<USER>/westside-bench/env/lib/python3.12/site-packages/pymysql/err.py", line 150, in raise_mysql_exception
    raise errorclass(errno, errval)
pymysql.err.OperationalError: (1045, "Access denied for user '_07fdbf6ba2f0be13'@'localhost' (using password: YES)")
2025-10-09 10:28:04,027 ERROR scheduler Exception in Enqueue Events for Site local.westside
Traceback (most recent call last):
  File "/home/<USER>/westside-bench/apps/frappe/frappe/utils/scheduler.py", line 99, in enqueue_events_for_site
    if is_scheduler_inactive():
       ^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/westside-bench/apps/frappe/frappe/utils/scheduler.py", line 143, in is_scheduler_inactive
    if is_scheduler_disabled(verbose=verbose):
       ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/westside-bench/apps/frappe/frappe/utils/scheduler.py", line 156, in is_scheduler_disabled
    frappe.db.get_single_value("System Settings", "enable_scheduler")
  File "/home/<USER>/westside-bench/apps/frappe/frappe/database/database.py", line 837, in get_single_value
    ).run()
      ^^^^^
  File "/home/<USER>/westside-bench/apps/frappe/frappe/query_builder/utils.py", line 87, in execute_query
    result = frappe.db.sql(query, params, *args, **kwargs)  # nosemgrep
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/westside-bench/apps/frappe/frappe/database/database.py", line 207, in sql
    self.connect()
  File "/home/<USER>/westside-bench/apps/frappe/frappe/database/database.py", line 112, in connect
    self._conn: "MariadbConnection" | "PostgresConnection" = self.get_connection()
                                                             ^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/westside-bench/apps/frappe/frappe/database/mariadb/database.py", line 107, in get_connection
    conn = self._get_connection()
           ^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/westside-bench/apps/frappe/frappe/database/mariadb/database.py", line 113, in _get_connection
    return self.create_connection()
           ^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/westside-bench/apps/frappe/frappe/database/mariadb/database.py", line 116, in create_connection
    return pymysql.connect(**self.get_connection_settings())
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/westside-bench/env/lib/python3.12/site-packages/pymysql/connections.py", line 361, in __init__
    self.connect()
  File "/home/<USER>/westside-bench/env/lib/python3.12/site-packages/pymysql/connections.py", line 669, in connect
    self._request_authentication()
  File "/home/<USER>/westside-bench/env/lib/python3.12/site-packages/pymysql/connections.py", line 957, in _request_authentication
    auth_packet = self._read_packet()
                  ^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/westside-bench/env/lib/python3.12/site-packages/pymysql/connections.py", line 775, in _read_packet
    packet.raise_for_error()
  File "/home/<USER>/westside-bench/env/lib/python3.12/site-packages/pymysql/protocol.py", line 219, in raise_for_error
    err.raise_mysql_exception(self._data)
  File "/home/<USER>/westside-bench/env/lib/python3.12/site-packages/pymysql/err.py", line 150, in raise_mysql_exception
    raise errorclass(errno, errval)
pymysql.err.OperationalError: (1045, "Access denied for user '_07fdbf6ba2f0be13'@'localhost' (using password: YES)")
2025-10-09 10:29:04,030 ERROR scheduler Exception in Enqueue Events for Site local.westside
Traceback (most recent call last):
  File "/home/<USER>/westside-bench/apps/frappe/frappe/utils/scheduler.py", line 99, in enqueue_events_for_site
    if is_scheduler_inactive():
       ^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/westside-bench/apps/frappe/frappe/utils/scheduler.py", line 143, in is_scheduler_inactive
    if is_scheduler_disabled(verbose=verbose):
       ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/westside-bench/apps/frappe/frappe/utils/scheduler.py", line 156, in is_scheduler_disabled
    frappe.db.get_single_value("System Settings", "enable_scheduler")
  File "/home/<USER>/westside-bench/apps/frappe/frappe/database/database.py", line 837, in get_single_value
    ).run()
      ^^^^^
  File "/home/<USER>/westside-bench/apps/frappe/frappe/query_builder/utils.py", line 87, in execute_query
    result = frappe.db.sql(query, params, *args, **kwargs)  # nosemgrep
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/westside-bench/apps/frappe/frappe/database/database.py", line 207, in sql
    self.connect()
  File "/home/<USER>/westside-bench/apps/frappe/frappe/database/database.py", line 112, in connect
    self._conn: "MariadbConnection" | "PostgresConnection" = self.get_connection()
                                                             ^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/westside-bench/apps/frappe/frappe/database/mariadb/database.py", line 107, in get_connection
    conn = self._get_connection()
           ^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/westside-bench/apps/frappe/frappe/database/mariadb/database.py", line 113, in _get_connection
    return self.create_connection()
           ^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/westside-bench/apps/frappe/frappe/database/mariadb/database.py", line 116, in create_connection
    return pymysql.connect(**self.get_connection_settings())
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/westside-bench/env/lib/python3.12/site-packages/pymysql/connections.py", line 361, in __init__
    self.connect()
  File "/home/<USER>/westside-bench/env/lib/python3.12/site-packages/pymysql/connections.py", line 669, in connect
    self._request_authentication()
  File "/home/<USER>/westside-bench/env/lib/python3.12/site-packages/pymysql/connections.py", line 957, in _request_authentication
    auth_packet = self._read_packet()
                  ^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/westside-bench/env/lib/python3.12/site-packages/pymysql/connections.py", line 775, in _read_packet
    packet.raise_for_error()
  File "/home/<USER>/westside-bench/env/lib/python3.12/site-packages/pymysql/protocol.py", line 219, in raise_for_error
    err.raise_mysql_exception(self._data)
  File "/home/<USER>/westside-bench/env/lib/python3.12/site-packages/pymysql/err.py", line 150, in raise_mysql_exception
    raise errorclass(errno, errval)
pymysql.err.OperationalError: (1045, "Access denied for user '_07fdbf6ba2f0be13'@'localhost' (using password: YES)")
2025-10-09 10:30:04,082 ERROR scheduler Exception in Enqueue Events for Site local.westside
Traceback (most recent call last):
  File "/home/<USER>/westside-bench/apps/frappe/frappe/utils/scheduler.py", line 99, in enqueue_events_for_site
    if is_scheduler_inactive():
       ^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/westside-bench/apps/frappe/frappe/utils/scheduler.py", line 143, in is_scheduler_inactive
    if is_scheduler_disabled(verbose=verbose):
       ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/westside-bench/apps/frappe/frappe/utils/scheduler.py", line 156, in is_scheduler_disabled
    frappe.db.get_single_value("System Settings", "enable_scheduler")
  File "/home/<USER>/westside-bench/apps/frappe/frappe/database/database.py", line 837, in get_single_value
    ).run()
      ^^^^^
  File "/home/<USER>/westside-bench/apps/frappe/frappe/query_builder/utils.py", line 87, in execute_query
    result = frappe.db.sql(query, params, *args, **kwargs)  # nosemgrep
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/westside-bench/apps/frappe/frappe/database/database.py", line 207, in sql
    self.connect()
  File "/home/<USER>/westside-bench/apps/frappe/frappe/database/database.py", line 112, in connect
    self._conn: "MariadbConnection" | "PostgresConnection" = self.get_connection()
                                                             ^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/westside-bench/apps/frappe/frappe/database/mariadb/database.py", line 107, in get_connection
    conn = self._get_connection()
           ^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/westside-bench/apps/frappe/frappe/database/mariadb/database.py", line 113, in _get_connection
    return self.create_connection()
           ^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/westside-bench/apps/frappe/frappe/database/mariadb/database.py", line 116, in create_connection
    return pymysql.connect(**self.get_connection_settings())
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/westside-bench/env/lib/python3.12/site-packages/pymysql/connections.py", line 361, in __init__
    self.connect()
  File "/home/<USER>/westside-bench/env/lib/python3.12/site-packages/pymysql/connections.py", line 669, in connect
    self._request_authentication()
  File "/home/<USER>/westside-bench/env/lib/python3.12/site-packages/pymysql/connections.py", line 957, in _request_authentication
    auth_packet = self._read_packet()
                  ^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/westside-bench/env/lib/python3.12/site-packages/pymysql/connections.py", line 775, in _read_packet
    packet.raise_for_error()
  File "/home/<USER>/westside-bench/env/lib/python3.12/site-packages/pymysql/protocol.py", line 219, in raise_for_error
    err.raise_mysql_exception(self._data)
  File "/home/<USER>/westside-bench/env/lib/python3.12/site-packages/pymysql/err.py", line 150, in raise_mysql_exception
    raise errorclass(errno, errval)
pymysql.err.OperationalError: (1045, "Access denied for user '_07fdbf6ba2f0be13'@'localhost' (using password: YES)")
2025-10-09 10:31:04,285 ERROR scheduler Exception in Enqueue Events for Site local.westside
Traceback (most recent call last):
  File "/home/<USER>/westside-bench/apps/frappe/frappe/utils/scheduler.py", line 99, in enqueue_events_for_site
    if is_scheduler_inactive():
       ^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/westside-bench/apps/frappe/frappe/utils/scheduler.py", line 143, in is_scheduler_inactive
    if is_scheduler_disabled(verbose=verbose):
       ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/westside-bench/apps/frappe/frappe/utils/scheduler.py", line 156, in is_scheduler_disabled
    frappe.db.get_single_value("System Settings", "enable_scheduler")
  File "/home/<USER>/westside-bench/apps/frappe/frappe/database/database.py", line 837, in get_single_value
    ).run()
      ^^^^^
  File "/home/<USER>/westside-bench/apps/frappe/frappe/query_builder/utils.py", line 87, in execute_query
    result = frappe.db.sql(query, params, *args, **kwargs)  # nosemgrep
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/westside-bench/apps/frappe/frappe/database/database.py", line 207, in sql
    self.connect()
  File "/home/<USER>/westside-bench/apps/frappe/frappe/database/database.py", line 112, in connect
    self._conn: "MariadbConnection" | "PostgresConnection" = self.get_connection()
                                                             ^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/westside-bench/apps/frappe/frappe/database/mariadb/database.py", line 107, in get_connection
    conn = self._get_connection()
           ^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/westside-bench/apps/frappe/frappe/database/mariadb/database.py", line 113, in _get_connection
    return self.create_connection()
           ^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/westside-bench/apps/frappe/frappe/database/mariadb/database.py", line 116, in create_connection
    return pymysql.connect(**self.get_connection_settings())
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/westside-bench/env/lib/python3.12/site-packages/pymysql/connections.py", line 361, in __init__
    self.connect()
  File "/home/<USER>/westside-bench/env/lib/python3.12/site-packages/pymysql/connections.py", line 669, in connect
    self._request_authentication()
  File "/home/<USER>/westside-bench/env/lib/python3.12/site-packages/pymysql/connections.py", line 957, in _request_authentication
    auth_packet = self._read_packet()
                  ^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/westside-bench/env/lib/python3.12/site-packages/pymysql/connections.py", line 775, in _read_packet
    packet.raise_for_error()
  File "/home/<USER>/westside-bench/env/lib/python3.12/site-packages/pymysql/protocol.py", line 219, in raise_for_error
    err.raise_mysql_exception(self._data)
  File "/home/<USER>/westside-bench/env/lib/python3.12/site-packages/pymysql/err.py", line 150, in raise_mysql_exception
    raise errorclass(errno, errval)
pymysql.err.OperationalError: (1045, "Access denied for user '_07fdbf6ba2f0be13'@'localhost' (using password: YES)")
2025-10-09 10:32:04,292 ERROR scheduler Exception in Enqueue Events for Site local.westside
Traceback (most recent call last):
  File "/home/<USER>/westside-bench/apps/frappe/frappe/utils/scheduler.py", line 99, in enqueue_events_for_site
    if is_scheduler_inactive():
       ^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/westside-bench/apps/frappe/frappe/utils/scheduler.py", line 143, in is_scheduler_inactive
    if is_scheduler_disabled(verbose=verbose):
       ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/westside-bench/apps/frappe/frappe/utils/scheduler.py", line 156, in is_scheduler_disabled
    frappe.db.get_single_value("System Settings", "enable_scheduler")
  File "/home/<USER>/westside-bench/apps/frappe/frappe/database/database.py", line 837, in get_single_value
    ).run()
      ^^^^^
  File "/home/<USER>/westside-bench/apps/frappe/frappe/query_builder/utils.py", line 87, in execute_query
    result = frappe.db.sql(query, params, *args, **kwargs)  # nosemgrep
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/westside-bench/apps/frappe/frappe/database/database.py", line 207, in sql
    self.connect()
  File "/home/<USER>/westside-bench/apps/frappe/frappe/database/database.py", line 112, in connect
    self._conn: "MariadbConnection" | "PostgresConnection" = self.get_connection()
                                                             ^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/westside-bench/apps/frappe/frappe/database/mariadb/database.py", line 107, in get_connection
    conn = self._get_connection()
           ^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/westside-bench/apps/frappe/frappe/database/mariadb/database.py", line 113, in _get_connection
    return self.create_connection()
           ^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/westside-bench/apps/frappe/frappe/database/mariadb/database.py", line 116, in create_connection
    return pymysql.connect(**self.get_connection_settings())
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/westside-bench/env/lib/python3.12/site-packages/pymysql/connections.py", line 361, in __init__
    self.connect()
  File "/home/<USER>/westside-bench/env/lib/python3.12/site-packages/pymysql/connections.py", line 669, in connect
    self._request_authentication()
  File "/home/<USER>/westside-bench/env/lib/python3.12/site-packages/pymysql/connections.py", line 957, in _request_authentication
    auth_packet = self._read_packet()
                  ^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/westside-bench/env/lib/python3.12/site-packages/pymysql/connections.py", line 775, in _read_packet
    packet.raise_for_error()
  File "/home/<USER>/westside-bench/env/lib/python3.12/site-packages/pymysql/protocol.py", line 219, in raise_for_error
    err.raise_mysql_exception(self._data)
  File "/home/<USER>/westside-bench/env/lib/python3.12/site-packages/pymysql/err.py", line 150, in raise_mysql_exception
    raise errorclass(errno, errval)
pymysql.err.OperationalError: (1045, "Access denied for user '_07fdbf6ba2f0be13'@'localhost' (using password: YES)")
2025-10-09 10:33:04,362 ERROR scheduler Exception in Enqueue Events for Site local.westside
Traceback (most recent call last):
  File "/home/<USER>/westside-bench/apps/frappe/frappe/utils/scheduler.py", line 99, in enqueue_events_for_site
    if is_scheduler_inactive():
       ^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/westside-bench/apps/frappe/frappe/utils/scheduler.py", line 143, in is_scheduler_inactive
    if is_scheduler_disabled(verbose=verbose):
       ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/westside-bench/apps/frappe/frappe/utils/scheduler.py", line 156, in is_scheduler_disabled
    frappe.db.get_single_value("System Settings", "enable_scheduler")
  File "/home/<USER>/westside-bench/apps/frappe/frappe/database/database.py", line 837, in get_single_value
    ).run()
      ^^^^^
  File "/home/<USER>/westside-bench/apps/frappe/frappe/query_builder/utils.py", line 87, in execute_query
    result = frappe.db.sql(query, params, *args, **kwargs)  # nosemgrep
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/westside-bench/apps/frappe/frappe/database/database.py", line 207, in sql
    self.connect()
  File "/home/<USER>/westside-bench/apps/frappe/frappe/database/database.py", line 112, in connect
    self._conn: "MariadbConnection" | "PostgresConnection" = self.get_connection()
                                                             ^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/westside-bench/apps/frappe/frappe/database/mariadb/database.py", line 107, in get_connection
    conn = self._get_connection()
           ^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/westside-bench/apps/frappe/frappe/database/mariadb/database.py", line 113, in _get_connection
    return self.create_connection()
           ^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/westside-bench/apps/frappe/frappe/database/mariadb/database.py", line 116, in create_connection
    return pymysql.connect(**self.get_connection_settings())
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/westside-bench/env/lib/python3.12/site-packages/pymysql/connections.py", line 361, in __init__
    self.connect()
  File "/home/<USER>/westside-bench/env/lib/python3.12/site-packages/pymysql/connections.py", line 669, in connect
    self._request_authentication()
  File "/home/<USER>/westside-bench/env/lib/python3.12/site-packages/pymysql/connections.py", line 957, in _request_authentication
    auth_packet = self._read_packet()
                  ^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/westside-bench/env/lib/python3.12/site-packages/pymysql/connections.py", line 775, in _read_packet
    packet.raise_for_error()
  File "/home/<USER>/westside-bench/env/lib/python3.12/site-packages/pymysql/protocol.py", line 219, in raise_for_error
    err.raise_mysql_exception(self._data)
  File "/home/<USER>/westside-bench/env/lib/python3.12/site-packages/pymysql/err.py", line 150, in raise_mysql_exception
    raise errorclass(errno, errval)
pymysql.err.OperationalError: (1045, "Access denied for user '_07fdbf6ba2f0be13'@'localhost' (using password: YES)")
2025-10-09 10:34:04,442 ERROR scheduler Exception in Enqueue Events for Site local.westside
Traceback (most recent call last):
  File "/home/<USER>/westside-bench/apps/frappe/frappe/utils/scheduler.py", line 99, in enqueue_events_for_site
    if is_scheduler_inactive():
       ^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/westside-bench/apps/frappe/frappe/utils/scheduler.py", line 143, in is_scheduler_inactive
    if is_scheduler_disabled(verbose=verbose):
       ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/westside-bench/apps/frappe/frappe/utils/scheduler.py", line 156, in is_scheduler_disabled
    frappe.db.get_single_value("System Settings", "enable_scheduler")
  File "/home/<USER>/westside-bench/apps/frappe/frappe/database/database.py", line 837, in get_single_value
    ).run()
      ^^^^^
  File "/home/<USER>/westside-bench/apps/frappe/frappe/query_builder/utils.py", line 87, in execute_query
    result = frappe.db.sql(query, params, *args, **kwargs)  # nosemgrep
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/westside-bench/apps/frappe/frappe/database/database.py", line 207, in sql
    self.connect()
  File "/home/<USER>/westside-bench/apps/frappe/frappe/database/database.py", line 112, in connect
    self._conn: "MariadbConnection" | "PostgresConnection" = self.get_connection()
                                                             ^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/westside-bench/apps/frappe/frappe/database/mariadb/database.py", line 107, in get_connection
    conn = self._get_connection()
           ^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/westside-bench/apps/frappe/frappe/database/mariadb/database.py", line 113, in _get_connection
    return self.create_connection()
           ^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/westside-bench/apps/frappe/frappe/database/mariadb/database.py", line 116, in create_connection
    return pymysql.connect(**self.get_connection_settings())
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/westside-bench/env/lib/python3.12/site-packages/pymysql/connections.py", line 361, in __init__
    self.connect()
  File "/home/<USER>/westside-bench/env/lib/python3.12/site-packages/pymysql/connections.py", line 669, in connect
    self._request_authentication()
  File "/home/<USER>/westside-bench/env/lib/python3.12/site-packages/pymysql/connections.py", line 957, in _request_authentication
    auth_packet = self._read_packet()
                  ^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/westside-bench/env/lib/python3.12/site-packages/pymysql/connections.py", line 775, in _read_packet
    packet.raise_for_error()
  File "/home/<USER>/westside-bench/env/lib/python3.12/site-packages/pymysql/protocol.py", line 219, in raise_for_error
    err.raise_mysql_exception(self._data)
  File "/home/<USER>/westside-bench/env/lib/python3.12/site-packages/pymysql/err.py", line 150, in raise_mysql_exception
    raise errorclass(errno, errval)
pymysql.err.OperationalError: (1045, "Access denied for user '_07fdbf6ba2f0be13'@'localhost' (using password: YES)")
2025-10-09 10:35:04,523 ERROR scheduler Exception in Enqueue Events for Site local.westside
Traceback (most recent call last):
  File "/home/<USER>/westside-bench/apps/frappe/frappe/utils/scheduler.py", line 99, in enqueue_events_for_site
    if is_scheduler_inactive():
       ^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/westside-bench/apps/frappe/frappe/utils/scheduler.py", line 143, in is_scheduler_inactive
    if is_scheduler_disabled(verbose=verbose):
       ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/westside-bench/apps/frappe/frappe/utils/scheduler.py", line 156, in is_scheduler_disabled
    frappe.db.get_single_value("System Settings", "enable_scheduler")
  File "/home/<USER>/westside-bench/apps/frappe/frappe/database/database.py", line 837, in get_single_value
    ).run()
      ^^^^^
  File "/home/<USER>/westside-bench/apps/frappe/frappe/query_builder/utils.py", line 87, in execute_query
    result = frappe.db.sql(query, params, *args, **kwargs)  # nosemgrep
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/westside-bench/apps/frappe/frappe/database/database.py", line 207, in sql
    self.connect()
  File "/home/<USER>/westside-bench/apps/frappe/frappe/database/database.py", line 112, in connect
    self._conn: "MariadbConnection" | "PostgresConnection" = self.get_connection()
                                                             ^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/westside-bench/apps/frappe/frappe/database/mariadb/database.py", line 107, in get_connection
    conn = self._get_connection()
           ^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/westside-bench/apps/frappe/frappe/database/mariadb/database.py", line 113, in _get_connection
    return self.create_connection()
           ^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/westside-bench/apps/frappe/frappe/database/mariadb/database.py", line 116, in create_connection
    return pymysql.connect(**self.get_connection_settings())
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/westside-bench/env/lib/python3.12/site-packages/pymysql/connections.py", line 361, in __init__
    self.connect()
  File "/home/<USER>/westside-bench/env/lib/python3.12/site-packages/pymysql/connections.py", line 669, in connect
    self._request_authentication()
  File "/home/<USER>/westside-bench/env/lib/python3.12/site-packages/pymysql/connections.py", line 957, in _request_authentication
    auth_packet = self._read_packet()
                  ^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/westside-bench/env/lib/python3.12/site-packages/pymysql/connections.py", line 775, in _read_packet
    packet.raise_for_error()
  File "/home/<USER>/westside-bench/env/lib/python3.12/site-packages/pymysql/protocol.py", line 219, in raise_for_error
    err.raise_mysql_exception(self._data)
  File "/home/<USER>/westside-bench/env/lib/python3.12/site-packages/pymysql/err.py", line 150, in raise_mysql_exception
    raise errorclass(errno, errval)
pymysql.err.OperationalError: (1045, "Access denied for user '_07fdbf6ba2f0be13'@'localhost' (using password: YES)")
2025-10-09 10:36:04,695 ERROR scheduler Exception in Enqueue Events for Site local.westside
Traceback (most recent call last):
  File "/home/<USER>/westside-bench/apps/frappe/frappe/utils/scheduler.py", line 99, in enqueue_events_for_site
    if is_scheduler_inactive():
       ^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/westside-bench/apps/frappe/frappe/utils/scheduler.py", line 143, in is_scheduler_inactive
    if is_scheduler_disabled(verbose=verbose):
       ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/westside-bench/apps/frappe/frappe/utils/scheduler.py", line 156, in is_scheduler_disabled
    frappe.db.get_single_value("System Settings", "enable_scheduler")
  File "/home/<USER>/westside-bench/apps/frappe/frappe/database/database.py", line 837, in get_single_value
    ).run()
      ^^^^^
  File "/home/<USER>/westside-bench/apps/frappe/frappe/query_builder/utils.py", line 87, in execute_query
    result = frappe.db.sql(query, params, *args, **kwargs)  # nosemgrep
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/westside-bench/apps/frappe/frappe/database/database.py", line 207, in sql
    self.connect()
  File "/home/<USER>/westside-bench/apps/frappe/frappe/database/database.py", line 112, in connect
    self._conn: "MariadbConnection" | "PostgresConnection" = self.get_connection()
                                                             ^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/westside-bench/apps/frappe/frappe/database/mariadb/database.py", line 107, in get_connection
    conn = self._get_connection()
           ^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/westside-bench/apps/frappe/frappe/database/mariadb/database.py", line 113, in _get_connection
    return self.create_connection()
           ^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/westside-bench/apps/frappe/frappe/database/mariadb/database.py", line 116, in create_connection
    return pymysql.connect(**self.get_connection_settings())
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/westside-bench/env/lib/python3.12/site-packages/pymysql/connections.py", line 361, in __init__
    self.connect()
  File "/home/<USER>/westside-bench/env/lib/python3.12/site-packages/pymysql/connections.py", line 669, in connect
    self._request_authentication()
  File "/home/<USER>/westside-bench/env/lib/python3.12/site-packages/pymysql/connections.py", line 957, in _request_authentication
    auth_packet = self._read_packet()
                  ^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/westside-bench/env/lib/python3.12/site-packages/pymysql/connections.py", line 775, in _read_packet
    packet.raise_for_error()
  File "/home/<USER>/westside-bench/env/lib/python3.12/site-packages/pymysql/protocol.py", line 219, in raise_for_error
    err.raise_mysql_exception(self._data)
  File "/home/<USER>/westside-bench/env/lib/python3.12/site-packages/pymysql/err.py", line 150, in raise_mysql_exception
    raise errorclass(errno, errval)
pymysql.err.OperationalError: (1045, "Access denied for user '_07fdbf6ba2f0be13'@'localhost' (using password: YES)")
2025-10-09 10:37:04,703 ERROR scheduler Exception in Enqueue Events for Site local.westside
Traceback (most recent call last):
  File "/home/<USER>/westside-bench/apps/frappe/frappe/utils/scheduler.py", line 99, in enqueue_events_for_site
    if is_scheduler_inactive():
       ^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/westside-bench/apps/frappe/frappe/utils/scheduler.py", line 143, in is_scheduler_inactive
    if is_scheduler_disabled(verbose=verbose):
       ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/westside-bench/apps/frappe/frappe/utils/scheduler.py", line 156, in is_scheduler_disabled
    frappe.db.get_single_value("System Settings", "enable_scheduler")
  File "/home/<USER>/westside-bench/apps/frappe/frappe/database/database.py", line 837, in get_single_value
    ).run()
      ^^^^^
  File "/home/<USER>/westside-bench/apps/frappe/frappe/query_builder/utils.py", line 87, in execute_query
    result = frappe.db.sql(query, params, *args, **kwargs)  # nosemgrep
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/westside-bench/apps/frappe/frappe/database/database.py", line 207, in sql
    self.connect()
  File "/home/<USER>/westside-bench/apps/frappe/frappe/database/database.py", line 112, in connect
    self._conn: "MariadbConnection" | "PostgresConnection" = self.get_connection()
                                                             ^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/westside-bench/apps/frappe/frappe/database/mariadb/database.py", line 107, in get_connection
    conn = self._get_connection()
           ^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/westside-bench/apps/frappe/frappe/database/mariadb/database.py", line 113, in _get_connection
    return self.create_connection()
           ^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/westside-bench/apps/frappe/frappe/database/mariadb/database.py", line 116, in create_connection
    return pymysql.connect(**self.get_connection_settings())
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/westside-bench/env/lib/python3.12/site-packages/pymysql/connections.py", line 361, in __init__
    self.connect()
  File "/home/<USER>/westside-bench/env/lib/python3.12/site-packages/pymysql/connections.py", line 669, in connect
    self._request_authentication()
  File "/home/<USER>/westside-bench/env/lib/python3.12/site-packages/pymysql/connections.py", line 957, in _request_authentication
    auth_packet = self._read_packet()
                  ^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/westside-bench/env/lib/python3.12/site-packages/pymysql/connections.py", line 775, in _read_packet
    packet.raise_for_error()
  File "/home/<USER>/westside-bench/env/lib/python3.12/site-packages/pymysql/protocol.py", line 219, in raise_for_error
    err.raise_mysql_exception(self._data)
  File "/home/<USER>/westside-bench/env/lib/python3.12/site-packages/pymysql/err.py", line 150, in raise_mysql_exception
    raise errorclass(errno, errval)
pymysql.err.OperationalError: (1045, "Access denied for user '_07fdbf6ba2f0be13'@'localhost' (using password: YES)")
2025-10-09 10:38:04,858 ERROR scheduler Exception in Enqueue Events for Site local.westside
Traceback (most recent call last):
  File "/home/<USER>/westside-bench/apps/frappe/frappe/utils/scheduler.py", line 99, in enqueue_events_for_site
    if is_scheduler_inactive():
       ^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/westside-bench/apps/frappe/frappe/utils/scheduler.py", line 143, in is_scheduler_inactive
    if is_scheduler_disabled(verbose=verbose):
       ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/westside-bench/apps/frappe/frappe/utils/scheduler.py", line 156, in is_scheduler_disabled
    frappe.db.get_single_value("System Settings", "enable_scheduler")
  File "/home/<USER>/westside-bench/apps/frappe/frappe/database/database.py", line 837, in get_single_value
    ).run()
      ^^^^^
  File "/home/<USER>/westside-bench/apps/frappe/frappe/query_builder/utils.py", line 87, in execute_query
    result = frappe.db.sql(query, params, *args, **kwargs)  # nosemgrep
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/westside-bench/apps/frappe/frappe/database/database.py", line 207, in sql
    self.connect()
  File "/home/<USER>/westside-bench/apps/frappe/frappe/database/database.py", line 112, in connect
    self._conn: "MariadbConnection" | "PostgresConnection" = self.get_connection()
                                                             ^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/westside-bench/apps/frappe/frappe/database/mariadb/database.py", line 107, in get_connection
    conn = self._get_connection()
           ^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/westside-bench/apps/frappe/frappe/database/mariadb/database.py", line 113, in _get_connection
    return self.create_connection()
           ^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/westside-bench/apps/frappe/frappe/database/mariadb/database.py", line 116, in create_connection
    return pymysql.connect(**self.get_connection_settings())
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/westside-bench/env/lib/python3.12/site-packages/pymysql/connections.py", line 361, in __init__
    self.connect()
  File "/home/<USER>/westside-bench/env/lib/python3.12/site-packages/pymysql/connections.py", line 669, in connect
    self._request_authentication()
  File "/home/<USER>/westside-bench/env/lib/python3.12/site-packages/pymysql/connections.py", line 957, in _request_authentication
    auth_packet = self._read_packet()
                  ^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/westside-bench/env/lib/python3.12/site-packages/pymysql/connections.py", line 775, in _read_packet
    packet.raise_for_error()
  File "/home/<USER>/westside-bench/env/lib/python3.12/site-packages/pymysql/protocol.py", line 219, in raise_for_error
    err.raise_mysql_exception(self._data)
  File "/home/<USER>/westside-bench/env/lib/python3.12/site-packages/pymysql/err.py", line 150, in raise_mysql_exception
    raise errorclass(errno, errval)
pymysql.err.OperationalError: (1045, "Access denied for user '_07fdbf6ba2f0be13'@'localhost' (using password: YES)")
2025-10-09 10:39:04,865 ERROR scheduler Exception in Enqueue Events for Site local.westside
Traceback (most recent call last):
  File "/home/<USER>/westside-bench/apps/frappe/frappe/utils/scheduler.py", line 99, in enqueue_events_for_site
    if is_scheduler_inactive():
       ^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/westside-bench/apps/frappe/frappe/utils/scheduler.py", line 143, in is_scheduler_inactive
    if is_scheduler_disabled(verbose=verbose):
       ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/westside-bench/apps/frappe/frappe/utils/scheduler.py", line 156, in is_scheduler_disabled
    frappe.db.get_single_value("System Settings", "enable_scheduler")
  File "/home/<USER>/westside-bench/apps/frappe/frappe/database/database.py", line 837, in get_single_value
    ).run()
      ^^^^^
  File "/home/<USER>/westside-bench/apps/frappe/frappe/query_builder/utils.py", line 87, in execute_query
    result = frappe.db.sql(query, params, *args, **kwargs)  # nosemgrep
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/westside-bench/apps/frappe/frappe/database/database.py", line 207, in sql
    self.connect()
  File "/home/<USER>/westside-bench/apps/frappe/frappe/database/database.py", line 112, in connect
    self._conn: "MariadbConnection" | "PostgresConnection" = self.get_connection()
                                                             ^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/westside-bench/apps/frappe/frappe/database/mariadb/database.py", line 107, in get_connection
    conn = self._get_connection()
           ^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/westside-bench/apps/frappe/frappe/database/mariadb/database.py", line 113, in _get_connection
    return self.create_connection()
           ^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/westside-bench/apps/frappe/frappe/database/mariadb/database.py", line 116, in create_connection
    return pymysql.connect(**self.get_connection_settings())
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/westside-bench/env/lib/python3.12/site-packages/pymysql/connections.py", line 361, in __init__
    self.connect()
  File "/home/<USER>/westside-bench/env/lib/python3.12/site-packages/pymysql/connections.py", line 669, in connect
    self._request_authentication()
  File "/home/<USER>/westside-bench/env/lib/python3.12/site-packages/pymysql/connections.py", line 957, in _request_authentication
    auth_packet = self._read_packet()
                  ^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/westside-bench/env/lib/python3.12/site-packages/pymysql/connections.py", line 775, in _read_packet
    packet.raise_for_error()
  File "/home/<USER>/westside-bench/env/lib/python3.12/site-packages/pymysql/protocol.py", line 219, in raise_for_error
    err.raise_mysql_exception(self._data)
  File "/home/<USER>/westside-bench/env/lib/python3.12/site-packages/pymysql/err.py", line 150, in raise_mysql_exception
    raise errorclass(errno, errval)
pymysql.err.OperationalError: (1045, "Access denied for user '_07fdbf6ba2f0be13'@'localhost' (using password: YES)")
2025-10-09 10:40:05,038 ERROR scheduler Exception in Enqueue Events for Site local.westside
Traceback (most recent call last):
  File "/home/<USER>/westside-bench/apps/frappe/frappe/utils/scheduler.py", line 99, in enqueue_events_for_site
    if is_scheduler_inactive():
       ^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/westside-bench/apps/frappe/frappe/utils/scheduler.py", line 143, in is_scheduler_inactive
    if is_scheduler_disabled(verbose=verbose):
       ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/westside-bench/apps/frappe/frappe/utils/scheduler.py", line 156, in is_scheduler_disabled
    frappe.db.get_single_value("System Settings", "enable_scheduler")
  File "/home/<USER>/westside-bench/apps/frappe/frappe/database/database.py", line 837, in get_single_value
    ).run()
      ^^^^^
  File "/home/<USER>/westside-bench/apps/frappe/frappe/query_builder/utils.py", line 87, in execute_query
    result = frappe.db.sql(query, params, *args, **kwargs)  # nosemgrep
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/westside-bench/apps/frappe/frappe/database/database.py", line 207, in sql
    self.connect()
  File "/home/<USER>/westside-bench/apps/frappe/frappe/database/database.py", line 112, in connect
    self._conn: "MariadbConnection" | "PostgresConnection" = self.get_connection()
                                                             ^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/westside-bench/apps/frappe/frappe/database/mariadb/database.py", line 107, in get_connection
    conn = self._get_connection()
           ^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/westside-bench/apps/frappe/frappe/database/mariadb/database.py", line 113, in _get_connection
    return self.create_connection()
           ^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/westside-bench/apps/frappe/frappe/database/mariadb/database.py", line 116, in create_connection
    return pymysql.connect(**self.get_connection_settings())
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/westside-bench/env/lib/python3.12/site-packages/pymysql/connections.py", line 361, in __init__
    self.connect()
  File "/home/<USER>/westside-bench/env/lib/python3.12/site-packages/pymysql/connections.py", line 669, in connect
    self._request_authentication()
  File "/home/<USER>/westside-bench/env/lib/python3.12/site-packages/pymysql/connections.py", line 957, in _request_authentication
    auth_packet = self._read_packet()
                  ^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/westside-bench/env/lib/python3.12/site-packages/pymysql/connections.py", line 775, in _read_packet
    packet.raise_for_error()
  File "/home/<USER>/westside-bench/env/lib/python3.12/site-packages/pymysql/protocol.py", line 219, in raise_for_error
    err.raise_mysql_exception(self._data)
  File "/home/<USER>/westside-bench/env/lib/python3.12/site-packages/pymysql/err.py", line 150, in raise_mysql_exception
    raise errorclass(errno, errval)
pymysql.err.OperationalError: (1045, "Access denied for user '_07fdbf6ba2f0be13'@'localhost' (using password: YES)")
2025-10-09 10:41:05,044 ERROR scheduler Exception in Enqueue Events for Site local.westside
Traceback (most recent call last):
  File "/home/<USER>/westside-bench/apps/frappe/frappe/utils/scheduler.py", line 99, in enqueue_events_for_site
    if is_scheduler_inactive():
       ^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/westside-bench/apps/frappe/frappe/utils/scheduler.py", line 143, in is_scheduler_inactive
    if is_scheduler_disabled(verbose=verbose):
       ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/westside-bench/apps/frappe/frappe/utils/scheduler.py", line 156, in is_scheduler_disabled
    frappe.db.get_single_value("System Settings", "enable_scheduler")
  File "/home/<USER>/westside-bench/apps/frappe/frappe/database/database.py", line 837, in get_single_value
    ).run()
      ^^^^^
  File "/home/<USER>/westside-bench/apps/frappe/frappe/query_builder/utils.py", line 87, in execute_query
    result = frappe.db.sql(query, params, *args, **kwargs)  # nosemgrep
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/westside-bench/apps/frappe/frappe/database/database.py", line 207, in sql
    self.connect()
  File "/home/<USER>/westside-bench/apps/frappe/frappe/database/database.py", line 112, in connect
    self._conn: "MariadbConnection" | "PostgresConnection" = self.get_connection()
                                                             ^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/westside-bench/apps/frappe/frappe/database/mariadb/database.py", line 107, in get_connection
    conn = self._get_connection()
           ^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/westside-bench/apps/frappe/frappe/database/mariadb/database.py", line 113, in _get_connection
    return self.create_connection()
           ^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/westside-bench/apps/frappe/frappe/database/mariadb/database.py", line 116, in create_connection
    return pymysql.connect(**self.get_connection_settings())
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/westside-bench/env/lib/python3.12/site-packages/pymysql/connections.py", line 361, in __init__
    self.connect()
  File "/home/<USER>/westside-bench/env/lib/python3.12/site-packages/pymysql/connections.py", line 669, in connect
    self._request_authentication()
  File "/home/<USER>/westside-bench/env/lib/python3.12/site-packages/pymysql/connections.py", line 957, in _request_authentication
    auth_packet = self._read_packet()
                  ^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/westside-bench/env/lib/python3.12/site-packages/pymysql/connections.py", line 775, in _read_packet
    packet.raise_for_error()
  File "/home/<USER>/westside-bench/env/lib/python3.12/site-packages/pymysql/protocol.py", line 219, in raise_for_error
    err.raise_mysql_exception(self._data)
  File "/home/<USER>/westside-bench/env/lib/python3.12/site-packages/pymysql/err.py", line 150, in raise_mysql_exception
    raise errorclass(errno, errval)
pymysql.err.OperationalError: (1045, "Access denied for user '_07fdbf6ba2f0be13'@'localhost' (using password: YES)")
2025-10-09 10:42:05,126 ERROR scheduler Exception in Enqueue Events for Site local.westside
Traceback (most recent call last):
  File "/home/<USER>/westside-bench/apps/frappe/frappe/utils/scheduler.py", line 99, in enqueue_events_for_site
    if is_scheduler_inactive():
       ^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/westside-bench/apps/frappe/frappe/utils/scheduler.py", line 143, in is_scheduler_inactive
    if is_scheduler_disabled(verbose=verbose):
       ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/westside-bench/apps/frappe/frappe/utils/scheduler.py", line 156, in is_scheduler_disabled
    frappe.db.get_single_value("System Settings", "enable_scheduler")
  File "/home/<USER>/westside-bench/apps/frappe/frappe/database/database.py", line 837, in get_single_value
    ).run()
      ^^^^^
  File "/home/<USER>/westside-bench/apps/frappe/frappe/query_builder/utils.py", line 87, in execute_query
    result = frappe.db.sql(query, params, *args, **kwargs)  # nosemgrep
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/westside-bench/apps/frappe/frappe/database/database.py", line 207, in sql
    self.connect()
  File "/home/<USER>/westside-bench/apps/frappe/frappe/database/database.py", line 112, in connect
    self._conn: "MariadbConnection" | "PostgresConnection" = self.get_connection()
                                                             ^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/westside-bench/apps/frappe/frappe/database/mariadb/database.py", line 107, in get_connection
    conn = self._get_connection()
           ^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/westside-bench/apps/frappe/frappe/database/mariadb/database.py", line 113, in _get_connection
    return self.create_connection()
           ^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/westside-bench/apps/frappe/frappe/database/mariadb/database.py", line 116, in create_connection
    return pymysql.connect(**self.get_connection_settings())
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/westside-bench/env/lib/python3.12/site-packages/pymysql/connections.py", line 361, in __init__
    self.connect()
  File "/home/<USER>/westside-bench/env/lib/python3.12/site-packages/pymysql/connections.py", line 669, in connect
    self._request_authentication()
  File "/home/<USER>/westside-bench/env/lib/python3.12/site-packages/pymysql/connections.py", line 957, in _request_authentication
    auth_packet = self._read_packet()
                  ^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/westside-bench/env/lib/python3.12/site-packages/pymysql/connections.py", line 775, in _read_packet
    packet.raise_for_error()
  File "/home/<USER>/westside-bench/env/lib/python3.12/site-packages/pymysql/protocol.py", line 219, in raise_for_error
    err.raise_mysql_exception(self._data)
  File "/home/<USER>/westside-bench/env/lib/python3.12/site-packages/pymysql/err.py", line 150, in raise_mysql_exception
    raise errorclass(errno, errval)
pymysql.err.OperationalError: (1045, "Access denied for user '_07fdbf6ba2f0be13'@'localhost' (using password: YES)")
2025-10-09 10:43:05,289 ERROR scheduler Exception in Enqueue Events for Site local.westside
Traceback (most recent call last):
  File "/home/<USER>/westside-bench/apps/frappe/frappe/utils/scheduler.py", line 99, in enqueue_events_for_site
    if is_scheduler_inactive():
       ^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/westside-bench/apps/frappe/frappe/utils/scheduler.py", line 143, in is_scheduler_inactive
    if is_scheduler_disabled(verbose=verbose):
       ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/westside-bench/apps/frappe/frappe/utils/scheduler.py", line 156, in is_scheduler_disabled
    frappe.db.get_single_value("System Settings", "enable_scheduler")
  File "/home/<USER>/westside-bench/apps/frappe/frappe/database/database.py", line 837, in get_single_value
    ).run()
      ^^^^^
  File "/home/<USER>/westside-bench/apps/frappe/frappe/query_builder/utils.py", line 87, in execute_query
    result = frappe.db.sql(query, params, *args, **kwargs)  # nosemgrep
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/westside-bench/apps/frappe/frappe/database/database.py", line 207, in sql
    self.connect()
  File "/home/<USER>/westside-bench/apps/frappe/frappe/database/database.py", line 112, in connect
    self._conn: "MariadbConnection" | "PostgresConnection" = self.get_connection()
                                                             ^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/westside-bench/apps/frappe/frappe/database/mariadb/database.py", line 107, in get_connection
    conn = self._get_connection()
           ^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/westside-bench/apps/frappe/frappe/database/mariadb/database.py", line 113, in _get_connection
    return self.create_connection()
           ^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/westside-bench/apps/frappe/frappe/database/mariadb/database.py", line 116, in create_connection
    return pymysql.connect(**self.get_connection_settings())
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/westside-bench/env/lib/python3.12/site-packages/pymysql/connections.py", line 361, in __init__
    self.connect()
  File "/home/<USER>/westside-bench/env/lib/python3.12/site-packages/pymysql/connections.py", line 669, in connect
    self._request_authentication()
  File "/home/<USER>/westside-bench/env/lib/python3.12/site-packages/pymysql/connections.py", line 957, in _request_authentication
    auth_packet = self._read_packet()
                  ^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/westside-bench/env/lib/python3.12/site-packages/pymysql/connections.py", line 775, in _read_packet
    packet.raise_for_error()
  File "/home/<USER>/westside-bench/env/lib/python3.12/site-packages/pymysql/protocol.py", line 219, in raise_for_error
    err.raise_mysql_exception(self._data)
  File "/home/<USER>/westside-bench/env/lib/python3.12/site-packages/pymysql/err.py", line 150, in raise_mysql_exception
    raise errorclass(errno, errval)
pymysql.err.OperationalError: (1045, "Access denied for user '_07fdbf6ba2f0be13'@'localhost' (using password: YES)")
2025-10-09 10:44:05,296 ERROR scheduler Exception in Enqueue Events for Site local.westside
Traceback (most recent call last):
  File "/home/<USER>/westside-bench/apps/frappe/frappe/utils/scheduler.py", line 99, in enqueue_events_for_site
    if is_scheduler_inactive():
       ^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/westside-bench/apps/frappe/frappe/utils/scheduler.py", line 143, in is_scheduler_inactive
    if is_scheduler_disabled(verbose=verbose):
       ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/westside-bench/apps/frappe/frappe/utils/scheduler.py", line 156, in is_scheduler_disabled
    frappe.db.get_single_value("System Settings", "enable_scheduler")
  File "/home/<USER>/westside-bench/apps/frappe/frappe/database/database.py", line 837, in get_single_value
    ).run()
      ^^^^^
  File "/home/<USER>/westside-bench/apps/frappe/frappe/query_builder/utils.py", line 87, in execute_query
    result = frappe.db.sql(query, params, *args, **kwargs)  # nosemgrep
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/westside-bench/apps/frappe/frappe/database/database.py", line 207, in sql
    self.connect()
  File "/home/<USER>/westside-bench/apps/frappe/frappe/database/database.py", line 112, in connect
    self._conn: "MariadbConnection" | "PostgresConnection" = self.get_connection()
                                                             ^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/westside-bench/apps/frappe/frappe/database/mariadb/database.py", line 107, in get_connection
    conn = self._get_connection()
           ^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/westside-bench/apps/frappe/frappe/database/mariadb/database.py", line 113, in _get_connection
    return self.create_connection()
           ^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/westside-bench/apps/frappe/frappe/database/mariadb/database.py", line 116, in create_connection
    return pymysql.connect(**self.get_connection_settings())
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/westside-bench/env/lib/python3.12/site-packages/pymysql/connections.py", line 361, in __init__
    self.connect()
  File "/home/<USER>/westside-bench/env/lib/python3.12/site-packages/pymysql/connections.py", line 669, in connect
    self._request_authentication()
  File "/home/<USER>/westside-bench/env/lib/python3.12/site-packages/pymysql/connections.py", line 957, in _request_authentication
    auth_packet = self._read_packet()
                  ^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/westside-bench/env/lib/python3.12/site-packages/pymysql/connections.py", line 775, in _read_packet
    packet.raise_for_error()
  File "/home/<USER>/westside-bench/env/lib/python3.12/site-packages/pymysql/protocol.py", line 219, in raise_for_error
    err.raise_mysql_exception(self._data)
  File "/home/<USER>/westside-bench/env/lib/python3.12/site-packages/pymysql/err.py", line 150, in raise_mysql_exception
    raise errorclass(errno, errval)
pymysql.err.OperationalError: (1045, "Access denied for user '_07fdbf6ba2f0be13'@'localhost' (using password: YES)")
2025-10-09 10:45:05,372 ERROR scheduler Exception in Enqueue Events for Site local.westside
Traceback (most recent call last):
  File "/home/<USER>/westside-bench/apps/frappe/frappe/utils/scheduler.py", line 99, in enqueue_events_for_site
    if is_scheduler_inactive():
       ^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/westside-bench/apps/frappe/frappe/utils/scheduler.py", line 143, in is_scheduler_inactive
    if is_scheduler_disabled(verbose=verbose):
       ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/westside-bench/apps/frappe/frappe/utils/scheduler.py", line 156, in is_scheduler_disabled
    frappe.db.get_single_value("System Settings", "enable_scheduler")
  File "/home/<USER>/westside-bench/apps/frappe/frappe/database/database.py", line 837, in get_single_value
    ).run()
      ^^^^^
  File "/home/<USER>/westside-bench/apps/frappe/frappe/query_builder/utils.py", line 87, in execute_query
    result = frappe.db.sql(query, params, *args, **kwargs)  # nosemgrep
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/westside-bench/apps/frappe/frappe/database/database.py", line 207, in sql
    self.connect()
  File "/home/<USER>/westside-bench/apps/frappe/frappe/database/database.py", line 112, in connect
    self._conn: "MariadbConnection" | "PostgresConnection" = self.get_connection()
                                                             ^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/westside-bench/apps/frappe/frappe/database/mariadb/database.py", line 107, in get_connection
    conn = self._get_connection()
           ^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/westside-bench/apps/frappe/frappe/database/mariadb/database.py", line 113, in _get_connection
    return self.create_connection()
           ^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/westside-bench/apps/frappe/frappe/database/mariadb/database.py", line 116, in create_connection
    return pymysql.connect(**self.get_connection_settings())
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/westside-bench/env/lib/python3.12/site-packages/pymysql/connections.py", line 361, in __init__
    self.connect()
  File "/home/<USER>/westside-bench/env/lib/python3.12/site-packages/pymysql/connections.py", line 669, in connect
    self._request_authentication()
  File "/home/<USER>/westside-bench/env/lib/python3.12/site-packages/pymysql/connections.py", line 957, in _request_authentication
    auth_packet = self._read_packet()
                  ^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/westside-bench/env/lib/python3.12/site-packages/pymysql/connections.py", line 775, in _read_packet
    packet.raise_for_error()
  File "/home/<USER>/westside-bench/env/lib/python3.12/site-packages/pymysql/protocol.py", line 219, in raise_for_error
    err.raise_mysql_exception(self._data)
  File "/home/<USER>/westside-bench/env/lib/python3.12/site-packages/pymysql/err.py", line 150, in raise_mysql_exception
    raise errorclass(errno, errval)
pymysql.err.OperationalError: (1045, "Access denied for user '_07fdbf6ba2f0be13'@'localhost' (using password: YES)")
2025-10-09 10:46:05,545 ERROR scheduler Exception in Enqueue Events for Site local.westside
Traceback (most recent call last):
  File "/home/<USER>/westside-bench/apps/frappe/frappe/utils/scheduler.py", line 99, in enqueue_events_for_site
    if is_scheduler_inactive():
       ^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/westside-bench/apps/frappe/frappe/utils/scheduler.py", line 143, in is_scheduler_inactive
    if is_scheduler_disabled(verbose=verbose):
       ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/westside-bench/apps/frappe/frappe/utils/scheduler.py", line 156, in is_scheduler_disabled
    frappe.db.get_single_value("System Settings", "enable_scheduler")
  File "/home/<USER>/westside-bench/apps/frappe/frappe/database/database.py", line 837, in get_single_value
    ).run()
      ^^^^^
  File "/home/<USER>/westside-bench/apps/frappe/frappe/query_builder/utils.py", line 87, in execute_query
    result = frappe.db.sql(query, params, *args, **kwargs)  # nosemgrep
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/westside-bench/apps/frappe/frappe/database/database.py", line 207, in sql
    self.connect()
  File "/home/<USER>/westside-bench/apps/frappe/frappe/database/database.py", line 112, in connect
    self._conn: "MariadbConnection" | "PostgresConnection" = self.get_connection()
                                                             ^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/westside-bench/apps/frappe/frappe/database/mariadb/database.py", line 107, in get_connection
    conn = self._get_connection()
           ^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/westside-bench/apps/frappe/frappe/database/mariadb/database.py", line 113, in _get_connection
    return self.create_connection()
           ^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/westside-bench/apps/frappe/frappe/database/mariadb/database.py", line 116, in create_connection
    return pymysql.connect(**self.get_connection_settings())
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/westside-bench/env/lib/python3.12/site-packages/pymysql/connections.py", line 361, in __init__
    self.connect()
  File "/home/<USER>/westside-bench/env/lib/python3.12/site-packages/pymysql/connections.py", line 669, in connect
    self._request_authentication()
  File "/home/<USER>/westside-bench/env/lib/python3.12/site-packages/pymysql/connections.py", line 957, in _request_authentication
    auth_packet = self._read_packet()
                  ^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/westside-bench/env/lib/python3.12/site-packages/pymysql/connections.py", line 775, in _read_packet
    packet.raise_for_error()
  File "/home/<USER>/westside-bench/env/lib/python3.12/site-packages/pymysql/protocol.py", line 219, in raise_for_error
    err.raise_mysql_exception(self._data)
  File "/home/<USER>/westside-bench/env/lib/python3.12/site-packages/pymysql/err.py", line 150, in raise_mysql_exception
    raise errorclass(errno, errval)
pymysql.err.OperationalError: (1045, "Access denied for user '_07fdbf6ba2f0be13'@'localhost' (using password: YES)")
2025-10-09 10:47:05,619 ERROR scheduler Exception in Enqueue Events for Site local.westside
Traceback (most recent call last):
  File "/home/<USER>/westside-bench/apps/frappe/frappe/utils/scheduler.py", line 99, in enqueue_events_for_site
    if is_scheduler_inactive():
       ^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/westside-bench/apps/frappe/frappe/utils/scheduler.py", line 143, in is_scheduler_inactive
    if is_scheduler_disabled(verbose=verbose):
       ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/westside-bench/apps/frappe/frappe/utils/scheduler.py", line 156, in is_scheduler_disabled
    frappe.db.get_single_value("System Settings", "enable_scheduler")
  File "/home/<USER>/westside-bench/apps/frappe/frappe/database/database.py", line 837, in get_single_value
    ).run()
      ^^^^^
  File "/home/<USER>/westside-bench/apps/frappe/frappe/query_builder/utils.py", line 87, in execute_query
    result = frappe.db.sql(query, params, *args, **kwargs)  # nosemgrep
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/westside-bench/apps/frappe/frappe/database/database.py", line 207, in sql
    self.connect()
  File "/home/<USER>/westside-bench/apps/frappe/frappe/database/database.py", line 112, in connect
    self._conn: "MariadbConnection" | "PostgresConnection" = self.get_connection()
                                                             ^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/westside-bench/apps/frappe/frappe/database/mariadb/database.py", line 107, in get_connection
    conn = self._get_connection()
           ^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/westside-bench/apps/frappe/frappe/database/mariadb/database.py", line 113, in _get_connection
    return self.create_connection()
           ^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/westside-bench/apps/frappe/frappe/database/mariadb/database.py", line 116, in create_connection
    return pymysql.connect(**self.get_connection_settings())
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/westside-bench/env/lib/python3.12/site-packages/pymysql/connections.py", line 361, in __init__
    self.connect()
  File "/home/<USER>/westside-bench/env/lib/python3.12/site-packages/pymysql/connections.py", line 669, in connect
    self._request_authentication()
  File "/home/<USER>/westside-bench/env/lib/python3.12/site-packages/pymysql/connections.py", line 957, in _request_authentication
    auth_packet = self._read_packet()
                  ^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/westside-bench/env/lib/python3.12/site-packages/pymysql/connections.py", line 775, in _read_packet
    packet.raise_for_error()
  File "/home/<USER>/westside-bench/env/lib/python3.12/site-packages/pymysql/protocol.py", line 219, in raise_for_error
    err.raise_mysql_exception(self._data)
  File "/home/<USER>/westside-bench/env/lib/python3.12/site-packages/pymysql/err.py", line 150, in raise_mysql_exception
    raise errorclass(errno, errval)
pymysql.err.OperationalError: (1045, "Access denied for user '_07fdbf6ba2f0be13'@'localhost' (using password: YES)")
2025-10-09 10:48:05,706 ERROR scheduler Exception in Enqueue Events for Site local.westside
Traceback (most recent call last):
  File "/home/<USER>/westside-bench/apps/frappe/frappe/utils/scheduler.py", line 99, in enqueue_events_for_site
    if is_scheduler_inactive():
       ^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/westside-bench/apps/frappe/frappe/utils/scheduler.py", line 143, in is_scheduler_inactive
    if is_scheduler_disabled(verbose=verbose):
       ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/westside-bench/apps/frappe/frappe/utils/scheduler.py", line 156, in is_scheduler_disabled
    frappe.db.get_single_value("System Settings", "enable_scheduler")
  File "/home/<USER>/westside-bench/apps/frappe/frappe/database/database.py", line 837, in get_single_value
    ).run()
      ^^^^^
  File "/home/<USER>/westside-bench/apps/frappe/frappe/query_builder/utils.py", line 87, in execute_query
    result = frappe.db.sql(query, params, *args, **kwargs)  # nosemgrep
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/westside-bench/apps/frappe/frappe/database/database.py", line 207, in sql
    self.connect()
  File "/home/<USER>/westside-bench/apps/frappe/frappe/database/database.py", line 112, in connect
    self._conn: "MariadbConnection" | "PostgresConnection" = self.get_connection()
                                                             ^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/westside-bench/apps/frappe/frappe/database/mariadb/database.py", line 107, in get_connection
    conn = self._get_connection()
           ^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/westside-bench/apps/frappe/frappe/database/mariadb/database.py", line 113, in _get_connection
    return self.create_connection()
           ^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/westside-bench/apps/frappe/frappe/database/mariadb/database.py", line 116, in create_connection
    return pymysql.connect(**self.get_connection_settings())
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/westside-bench/env/lib/python3.12/site-packages/pymysql/connections.py", line 361, in __init__
    self.connect()
  File "/home/<USER>/westside-bench/env/lib/python3.12/site-packages/pymysql/connections.py", line 669, in connect
    self._request_authentication()
  File "/home/<USER>/westside-bench/env/lib/python3.12/site-packages/pymysql/connections.py", line 957, in _request_authentication
    auth_packet = self._read_packet()
                  ^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/westside-bench/env/lib/python3.12/site-packages/pymysql/connections.py", line 775, in _read_packet
    packet.raise_for_error()
  File "/home/<USER>/westside-bench/env/lib/python3.12/site-packages/pymysql/protocol.py", line 219, in raise_for_error
    err.raise_mysql_exception(self._data)
  File "/home/<USER>/westside-bench/env/lib/python3.12/site-packages/pymysql/err.py", line 150, in raise_mysql_exception
    raise errorclass(errno, errval)
pymysql.err.OperationalError: (1045, "Access denied for user '_07fdbf6ba2f0be13'@'localhost' (using password: YES)")
