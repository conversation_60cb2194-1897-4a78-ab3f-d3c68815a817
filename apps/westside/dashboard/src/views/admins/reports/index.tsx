import { Button } from "@/components/ui/button";
import {
  Command,
  CommandEmpty,
  CommandGroup,
  CommandInput,
  CommandItem,
  CommandList,
} from "@/components/ui/command";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import {
  Select,
  SelectContent,
  SelectGroup,
  SelectItem,
  SelectLabel,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  fetchAllCustomers,
  fetchReportList,
  submitExportToExcel,
} from "@/services/admin/reports";
import { useMutation, useQuery } from "@tanstack/react-query";
import dayjs from "dayjs";
import { Check, ChevronsUpDown } from "lucide-react";
import { useMemo, useState } from "react";
import { useSearchParams } from "react-router-dom";
import ReportDetailCard from "./ReportDetailCard";
import Loader from "@/components/Loader";
import { Typography } from "@/components/typography";
import { toast } from "sonner";
import { ExcelPreviewModal } from "./ExcelPreviewModal";

const ReportsView = () => {
  const [searchParams, setSearchParams] = useSearchParams();
  const customerParam = searchParams.get("customer") || "";
  const startDateParam = searchParams.get("sd") || "";
  const endDateParam = searchParams.get("ed") || "";

  // Customer combobox state
  const [customer, setCustomer] = useState({
    name: customerParam || "",
    displayName: "",
  });
  const [customerQuery, setCustomerQuery] = useState("");
  const [customerOpen, setCustomerOpen] = useState(false);

  const [previewUrl, setPreviewUrl] = useState<string | null>(null);
  const [modalOpen, setModalOpen] = useState(false);

  const {
    data: customerData,
    isFetching: isCustomerDataFetching,
    isError: isCustomerDataError,
  } = useQuery({
    queryKey: ["fetchAllCustomers"],
    queryFn: fetchAllCustomers,
  });

  const updateParam = (key: string, value: string) => {
    const params = new URLSearchParams(searchParams);
    if (value) {
      params.set(key, value);
    } else {
      params.delete(key);
    }
    setSearchParams(params);
  };

  // Filter customers based on search query
  const filteredCustomers = useMemo(() => {
    if (!customerQuery) return customerData?.message?.data;
    return customerData?.message?.data?.filter((customer) =>
      customer.customer_name
        ?.toLowerCase()
        ?.includes(customerQuery.toLowerCase())
    );
  }, [customerQuery, customerData]);

  const handleCustomerSearch = (val: string) => {
    setCustomerQuery(val);
  };

  // Fetch My Booking Data.
  const {
    data: reportData,
    isFetching: isReportDataFetching,
    isError: isReportDataError,
  } = useQuery({
    queryKey: [
      "fetchReportData",
      {
        customer: customerParam || "",
        sd: startDateParam || dayjs().subtract(1, "month").format("YYYY-MM-DD"),
        ed: endDateParam || dayjs().format("YYYY-MM-DD"),
      },
    ],
    queryFn: fetchReportList,
    refetchOnWindowFocus: false,
    enabled: true, // Always fetch data - shows all customers when none selected
  });

  // Submit Export to Excel.
  const { mutate, isPending, isError, isSuccess } = useMutation({
    mutationFn: submitExportToExcel,
    onSuccess: (data) => {
      if (data?.message?.status_code === 200) {
        toast.success("Export to excel is successful!");
        const fileUrl = data?.message?.file_url;
        if (fileUrl) {
          setPreviewUrl(fileUrl);
          setModalOpen(false);

          // Trigger file download
          const link = document.createElement("a");
          link.href = fileUrl;
          link.download = "";
          document.body.appendChild(link);
          link.click();
          document.body.removeChild(link);
        }
      } else {
        toast.error(
          data?.message?.message || "Failed to export excel. Please try again."
        );
      }
    },
    onError: () => {
      toast.error("Failed to export excel. Please try again.");
    },
  });

  // Check if we have data to export
  const hasDataToExport = (reportData?.message?.data?.length ?? 0) > 0;

  return (
    <div className="">
      <div className="flex items-end justify-between gap-2">
        {/* Left section */}
        <div className=""></div>
        {/* Right Section */}
        <div className="flex items-end gap-4">
          <div className="">
            <Button
              onClick={() => {
                const params = new URLSearchParams(searchParams);
                params.delete("customer");
                params.delete("sd");
                params.delete("ed");
                setSearchParams(params);
                setCustomer({
                  name: "",
                  displayName: "",
                });
              }}
              variant={"ghost"}
              className="underline hover:shadow"
            >
              Clear Filters
            </Button>
          </div>
          <div className="space-y-1">
            <Label>Choose Customer</Label>
            <Popover open={customerOpen} onOpenChange={setCustomerOpen}>
              <PopoverTrigger asChild>
                <Button
                  variant="outline"
                  role="combobox"
                  aria-expanded={customerOpen}
                  className="w-full justify-between h-11 min-w-48"
                >
                  {customer.name
                    ? customerData?.message?.data?.find(
                        (c) => c.id === customer.name
                      )?.customer_name
                    : "Select Customer..."}
                  <ChevronsUpDown className="opacity-50" />
                </Button>
              </PopoverTrigger>
              <PopoverContent className="w-full p-0">
                <Command>
                  <CommandInput
                    placeholder="Search Customer..."
                    className="h-9"
                    value={customerQuery}
                    onValueChange={(val) => handleCustomerSearch(val)}
                  />
                  <CommandList>
                    <CommandEmpty>No Customer Found.</CommandEmpty>
                    <CommandGroup>
                      {filteredCustomers?.map((customerItem) => (
                        <CommandItem
                          key={customerItem.id}
                          value={customerItem.customer_name}
                          onSelect={() => {
                            setCustomer({
                              name: customerItem.id,
                              displayName: customerItem.customer_name,
                            });
                            setCustomerOpen(false);
                            updateParam("customer", customerItem.id);
                          }}
                        >
                          {customerItem.customer_name}
                          <Check
                            className={
                              customer.name === customerItem.id
                                ? "ml-auto opacity-100"
                                : "ml-auto opacity-0"
                            }
                          />
                        </CommandItem>
                      ))}
                    </CommandGroup>
                  </CommandList>
                </Command>
              </PopoverContent>
            </Popover>
          </div>
          <div className="space-y-1">
            <Label className="whitespace-nowrap">Choose Parent</Label>
            <Select
              value={""}
              onValueChange={(data) => updateParam("bookingStatus", data)}
            >
              <SelectTrigger className="min-w-36">
                <SelectValue placeholder="Choose Parent" />
              </SelectTrigger>
              <SelectContent>
                <SelectGroup>
                  <SelectLabel>Parent</SelectLabel>
                  <SelectItem value={"all"}>All</SelectItem>
                </SelectGroup>
              </SelectContent>
            </Select>
          </div>
          <div className="space-y-1">
            <Label>Start Date</Label>
            <Input
              onChange={(e) => {
                updateParam("sd", e.target.value);
              }}
              value={
                startDateParam ||
                dayjs().subtract(1, "month").format("YYYY-MM-DD")
              }
              className=""
              placeholder="Start Date"
              type="date"
            />
          </div>
          <div className="space-y-1">
            <Label>End Date</Label>
            <Input
              onChange={(e) => {
                updateParam("ed", e.target.value);
              }}
              value={endDateParam || dayjs().format("YYYY-MM-DD")}
              className=""
              placeholder="End Date"
              type="date"
            />
          </div>
          <div className="">
            <Button
              disabled={isPending || !hasDataToExport}
              onClick={() =>
                mutate({
                  customer: customerParam || "", // Allow empty customer for all customers export
                  sd: startDateParam || dayjs().subtract(1, "month").format("YYYY-MM-DD"),
                  ed: endDateParam || dayjs().format("YYYY-MM-DD"),
                })
              }
              className={isPending ? "cursor-not-allowed animate-pulse" : ""}
            >
              Export to Excel
            </Button>
          </div>
        </div>
      </div>
      {/* List Section */}
      {isReportDataFetching ? (
        <div className="py-10 flex justify-center">
          <Loader />
        </div>
      ) : (
        <div className="py-7">
          {hasDataToExport ? (
            <ReportDetailCard reports={reportData?.message?.data || []} />
          ) : (
            <p className="text-center text-gray-500"></p>
          )}
        </div>
      )}
      {!hasDataToExport && !isReportDataFetching ? (
        <div className="py-10 flex justify-center">
          <Typography variant={"muted"}>No Reports Found!.</Typography>
        </div>
      ) : (
        ""
      )}
      {previewUrl && (
        <ExcelPreviewModal
          fileTitle="Customer"
          fileUrl={previewUrl}
          open={modalOpen}
          onOpenChange={setModalOpen}
        />
      )}
    </div>
  );
};

export default ReportsView;